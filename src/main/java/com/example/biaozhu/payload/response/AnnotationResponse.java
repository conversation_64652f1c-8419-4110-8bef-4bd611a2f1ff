package com.example.biaozhu.payload.response;

import com.example.biaozhu.entity.Annotation;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * 标注响应类
 */
public class AnnotationResponse {

    private Long id;
    private Long dataItemId;
    private String dataItemName;
    private String dataItemFilePath;
    private Long taskId;
    private String taskName;
    private String content;
    private String type;
    private String status;
    private double confidence;
    private UserSummary creator;
    private UserSummary reviewer;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reviewedAt;
    
    private String reviewComments;

    public AnnotationResponse() {
    }

    public AnnotationResponse(Annotation annotation) {
        this.id = annotation.getId();
        this.dataItemId = annotation.getDataItem().getId();
        this.dataItemName = annotation.getDataItem().getName();
        this.dataItemFilePath = annotation.getDataItem().getFilePath();
        
        if (annotation.getTask() != null) {
            this.taskId = annotation.getTask().getId();
            this.taskName = annotation.getTask().getName();
        }
        
        this.content = annotation.getContent();
        this.type = annotation.getType();
        this.status = annotation.getStatus();
        this.confidence = annotation.getConfidence();
        
        if (annotation.getCreator() != null) {
            this.creator = new UserSummary(
                annotation.getCreator().getId(),
                annotation.getCreator().getUsername(),
                annotation.getCreator().getFullName()
            );
        }
        
        if (annotation.getReviewer() != null) {
            this.reviewer = new UserSummary(
                annotation.getReviewer().getId(),
                annotation.getReviewer().getUsername(),
                annotation.getReviewer().getFullName()
            );
        }
        
        this.createdAt = annotation.getCreatedAt();
        this.updatedAt = annotation.getUpdatedAt();
        this.reviewedAt = annotation.getReviewedAt();
        this.reviewComments = annotation.getReviewComments();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDataItemId() {
        return dataItemId;
    }

    public void setDataItemId(Long dataItemId) {
        this.dataItemId = dataItemId;
    }

    public String getDataItemName() {
        return dataItemName;
    }

    public void setDataItemName(String dataItemName) {
        this.dataItemName = dataItemName;
    }

    public String getDataItemFilePath() {
        return dataItemFilePath;
    }

    public void setDataItemFilePath(String dataItemFilePath) {
        this.dataItemFilePath = dataItemFilePath;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public double getConfidence() {
        return confidence;
    }

    public void setConfidence(double confidence) {
        this.confidence = confidence;
    }

    public UserSummary getCreator() {
        return creator;
    }

    public void setCreator(UserSummary creator) {
        this.creator = creator;
    }

    public UserSummary getReviewer() {
        return reviewer;
    }

    public void setReviewer(UserSummary reviewer) {
        this.reviewer = reviewer;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public LocalDateTime getReviewedAt() {
        return reviewedAt;
    }

    public void setReviewedAt(LocalDateTime reviewedAt) {
        this.reviewedAt = reviewedAt;
    }

    public String getReviewComments() {
        return reviewComments;
    }

    public void setReviewComments(String reviewComments) {
        this.reviewComments = reviewComments;
    }
} 