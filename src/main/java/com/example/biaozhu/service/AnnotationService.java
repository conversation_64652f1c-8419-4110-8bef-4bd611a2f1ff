package com.example.biaozhu.service;

import com.example.biaozhu.entity.Annotation;
import com.example.biaozhu.entity.User;
import com.example.biaozhu.payload.request.AnnotationRequest;
import com.example.biaozhu.payload.request.BatchReviewRequest;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Map;

/**
 * 标注服务接口
 * 定义标注相关的所有操作
 */
public interface AnnotationService {

    /**
     * 创建新标注
     * 
     * @param annotationRequest 标注请求对象
     * @param user 创建标注的用户
     * @return 创建的标注对象
     */
    Annotation createAnnotation(AnnotationRequest annotationRequest, User user);
    
    /**
     * 批量创建标注
     * 
     * @param annotationRequests 标注请求对象列表
     * @param user 创建标注的用户
     * @return 创建的标注对象列表
     */
    List<Annotation> createAnnotationsBatch(List<AnnotationRequest> annotationRequests, User user);
    
    /**
     * 获取所有标注（分页）
     * 
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 标注分页结果
     */
    Page<Annotation> getAllAnnotations(int page, int size, String sort);
    
    /**
     * 获取用户的所有标注（分页）
     * 
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 标注分页结果
     */
    Page<Annotation> getAnnotationsByUser(Long userId, int page, int size, String sort);
    
    /**
     * 获取任务的所有标注（分页）
     * 
     * @param taskId 任务ID
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 标注分页结果
     */
    Page<Annotation> getAnnotationsByTask(Long taskId, int page, int size, String sort);
    
    /**
     * 获取数据项的所有标注
     * 
     * @param dataItemId 数据项ID
     * @return 标注列表
     */
    List<Annotation> getAnnotationsByDataItem(Long dataItemId);
    
    /**
     * 根据ID获取标注
     * 
     * @param id 标注ID
     * @return 标注对象
     */
    Annotation getAnnotationById(Long id);
    
    /**
     * 更新标注
     * 
     * @param id 标注ID
     * @param annotationRequest 标注请求对象
     * @return 更新后的标注对象
     */
    Annotation updateAnnotation(Long id, AnnotationRequest annotationRequest);
    
    /**
     * 删除标注
     * 
     * @param id 标注ID
     */
    void deleteAnnotation(Long id);
    
    /**
     * 批量删除标注
     * 
     * @param ids 标注ID列表
     * @return 删除的标注数量
     */
    int deleteAnnotationsBatch(List<Long> ids);
    
    /**
     * 审核标注
     * 
     * @param id 标注ID
     * @param reviewer 审核人
     * @param approved 是否通过
     * @param comments 审核意见
     */
    void reviewAnnotation(Long id, User reviewer, boolean approved, String comments);
    
    /**
     * 批量审核标注
     *
     * @param ids 标注ID列表
     * @param reviewer 审核人
     * @param approved 是否通过
     * @param comments 审核意见
     * @return 审核的标注数量
     */
    int reviewAnnotationsBatch(List<Long> ids, User reviewer, boolean approved, String comments);

    /**
     * 批量审核标注（支持不同状态）
     *
     * @param batchReviewRequest 批量审核请求对象
     * @param reviewer 审核人
     * @return 审核结果信息
     */
    Map<String, Object> reviewAnnotationsBatchWithDifferentStatus(BatchReviewRequest batchReviewRequest, User reviewer);
    
    /**
     * 导出标注数据
     * 
     * @param taskId 任务ID
     * @param format 导出格式
     * @return 导出的响应体
     */
    ResponseEntity<?> exportAnnotations(Long taskId, String format);
    
    /**
     * 获取标注统计信息
     * 
     * @param taskId 任务ID
     * @return 标注统计信息
     */
    Map<String, Object> getAnnotationStatistics(Long taskId);
    
    /**
     * 获取用户标注性能统计
     * 
     * @param userId 用户ID
     * @param days 统计天数
     * @return 标注性能统计
     */
    Map<String, Object> getUserAnnotationPerformance(Long userId, int days);
    
    /**
     * 检查用户是否是标注创建者
     *
     * @param annotationId 标注ID
     * @return 是否是创建者
     */
    boolean isAnnotationCreator(Long annotationId);

    /**
     * 获取任务下未审核的标注记录（分页）
     *
     * @param taskId 任务ID
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 未审核标注分页结果
     */
    Page<Annotation> getUnreviewedAnnotationsByTask(Long taskId, int page, int size, String sort);
} 