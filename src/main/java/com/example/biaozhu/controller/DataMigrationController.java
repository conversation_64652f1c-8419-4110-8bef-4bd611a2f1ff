package com.example.biaozhu.controller;

import com.example.biaozhu.entity.Dataset;
import com.example.biaozhu.entity.Project;
import com.example.biaozhu.entity.User;
import com.example.biaozhu.payload.response.DatasetResponse;
import com.example.biaozhu.payload.response.MessageResponse;
import com.example.biaozhu.repository.DatasetRepository;
import com.example.biaozhu.repository.ProjectRepository;
import com.example.biaozhu.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.UUID;

/**
 * 数据迁移控制器
 * 负责从localStorage迁移数据到数据库
 */
@RestController
@RequestMapping("/migration")
public class DataMigrationController {

    private final DatasetRepository datasetRepository;
    private final ProjectRepository projectRepository;
    private final UserRepository userRepository;

    @Autowired
    public DataMigrationController(
            DatasetRepository datasetRepository,
            ProjectRepository projectRepository,
            UserRepository userRepository) {
        this.datasetRepository = datasetRepository;
        this.projectRepository = projectRepository;
        this.userRepository = userRepository;
    }

    /**
     * 将数据集从localStorage迁移到数据库
     * 不需要特殊权限，任何已认证用户都可以使用
     */
    @PostMapping("/datasets")
    public ResponseEntity<?> migrateDataset(@RequestBody Map<String, Object> datasetData) {
        try {
            // 获取当前用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !authentication.isAuthenticated()) {
                return ResponseEntity.badRequest().body(new MessageResponse("未认证用户"));
            }
            
            User currentUser = userRepository.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("找不到当前用户"));
            
            // 检查必要的字段
            if (!datasetData.containsKey("name") || datasetData.get("name") == null) {
                return ResponseEntity.badRequest().body(new MessageResponse("数据集名称不能为空"));
            }
            
            if (!datasetData.containsKey("projectId") || datasetData.get("projectId") == null) {
                return ResponseEntity.badRequest().body(new MessageResponse("项目ID不能为空"));
            }
            
            // 获取数据集名称
            String name = datasetData.get("name").toString();
            
            // 检查数据集名称是否已存在
            Optional<Dataset> existingDataset = datasetRepository.findByName(name);
            if (existingDataset.isPresent()) {
                return ResponseEntity.badRequest().body(new MessageResponse("数据集名称已存在: " + name));
            }
            
            // 创建新数据集
            Dataset dataset = new Dataset();
            dataset.setName(name);
            
            // 设置描述
            if (datasetData.containsKey("description")) {
                dataset.setDescription(datasetData.get("description").toString());
            }
            
            // 设置类型
            if (datasetData.containsKey("type")) {
                dataset.setType(datasetData.get("type").toString());
            } else {
                dataset.setType("TEXT"); // 默认类型
            }
            
            // 设置项目
            Long projectId;
            try {
                if (datasetData.get("projectId") instanceof Number) {
                    projectId = ((Number) datasetData.get("projectId")).longValue();
                } else {
                    projectId = Long.parseLong(datasetData.get("projectId").toString());
                }
                
                Project project = projectRepository.findById(projectId)
                        .orElseThrow(() -> new RuntimeException("找不到项目，ID: " + projectId));
                dataset.setProject(project);
            } catch (Exception e) {
                return ResponseEntity.badRequest().body(new MessageResponse("项目ID无效: " + e.getMessage()));
            }
            
            // 设置创建者
            dataset.setCreator(currentUser);
            
            // 设置时间戳
            dataset.setCreatedAt(LocalDateTime.now());
            dataset.setUpdatedAt(LocalDateTime.now());
            
            // 设置计数
            if (datasetData.containsKey("itemCount")) {
                try {
                    if (datasetData.get("itemCount") instanceof Number) {
                        dataset.setItemCount(((Number) datasetData.get("itemCount")).intValue());
                    } else {
                        dataset.setItemCount(Integer.parseInt(datasetData.get("itemCount").toString()));
                    }
                } catch (NumberFormatException e) {
                    dataset.setItemCount(0);
                }
            }
            
            // 设置可见性
            if (datasetData.containsKey("isPublic")) {
                dataset.setPublic(Boolean.parseBoolean(datasetData.get("isPublic").toString()));
            } else {
                dataset.setPublic(false);
            }
            
            // 保存数据集
            Dataset savedDataset = datasetRepository.save(dataset);
            
            return ResponseEntity.ok(new DatasetResponse(savedDataset));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().body(new MessageResponse("迁移失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取所有数据集
     * 不经过权限过滤，直接从数据库读取
     */
    @GetMapping("/datasets/list")
    public ResponseEntity<?> getAllDatasets() {
        try {
            // 获取当前用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !authentication.isAuthenticated()) {
                return ResponseEntity.badRequest().body(new MessageResponse("未认证用户"));
            }
            
            // 直接从数据库获取所有数据集
            List<Dataset> datasets = datasetRepository.findAll();
            
            // 转换为响应对象
            List<DatasetResponse> responses = datasets.stream()
                .map(DatasetResponse::new)
                .collect(Collectors.toList());
            
            return ResponseEntity.ok(responses);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().body(new MessageResponse("获取数据集失败: " + e.getMessage()));
        }
    }
    
    /**
     * 数据集文件导入
     * 不需要特殊权限检查，任何已认证用户都可以使用
     */
    @PostMapping("/datasets/{id}/import")
    public ResponseEntity<?> importDatasetFile(
            @PathVariable Long id,
            @RequestParam("file") MultipartFile file) {
        try {
            // 获取当前用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !authentication.isAuthenticated()) {
                return ResponseEntity.badRequest().body(new MessageResponse("未认证用户"));
            }
            
            // 获取数据集
            Dataset dataset = datasetRepository.findById(id)
                    .orElseThrow(() -> new RuntimeException("找不到数据集，ID: " + id));
            
            // 创建数据集目录（如果不存在）
            String uploadDir = "uploads/datasets/" + id;
            Path uploadPath = Paths.get(uploadDir);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }
            
            // 生成唯一的文件名
            String uniqueFileName = UUID.randomUUID().toString() + "_" + file.getOriginalFilename();
            Path filePath = uploadPath.resolve(uniqueFileName);
            
            // 保存文件
            Files.copy(file.getInputStream(), filePath);
            
            // 增加数据集项目计数
            Integer itemCount = dataset.getItemCount();
            int currentCount = (itemCount != null) ? itemCount : 0;
            dataset.setItemCount(currentCount + 1);
            datasetRepository.save(dataset);
            
            // 返回响应
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "文件导入成功: " + file.getOriginalFilename());
            response.put("filePath", filePath.toString());
            
            return ResponseEntity.ok(response);
        } catch (IOException e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().body(new MessageResponse("文件导入失败: " + e.getMessage()));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().body(new MessageResponse("导入处理失败: " + e.getMessage()));
        }
    }
    
    /**
     * 简单文件上传测试端点
     * 用于测试文件上传功能
     */
    @PostMapping("/upload-test")
    public ResponseEntity<?> uploadTest(@RequestParam("file") MultipartFile file) {
        try {
            System.out.println("接收到文件上传测试请求");
            System.out.println("文件名: " + file.getOriginalFilename());
            System.out.println("文件大小: " + file.getSize() + " 字节");
            System.out.println("内容类型: " + file.getContentType());

            // 创建测试目录
            String uploadDir = "uploads/test";
            Path uploadPath = Paths.get(uploadDir);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }
            
            // 保存文件
            String fileName = UUID.randomUUID().toString() + "_" + file.getOriginalFilename();
            Path filePath = uploadPath.resolve(fileName);
            Files.copy(file.getInputStream(), filePath);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "测试文件上传成功");
            response.put("fileName", fileName);
            response.put("originalName", file.getOriginalFilename());
            response.put("size", file.getSize());
            response.put("path", filePath.toString());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            e.printStackTrace();
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "测试文件上传失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 健康检查端点
     */
    @GetMapping("/health")
    public ResponseEntity<?> healthCheck() {
        Map<String, String> response = new HashMap<>();
        response.put("status", "UP");
        response.put("message", "数据迁移服务正常");
        response.put("timestamp", LocalDateTime.now().toString());
        return ResponseEntity.ok(response);
    }
} 