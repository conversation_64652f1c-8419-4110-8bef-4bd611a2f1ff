package com.example.biaozhu.payload.request;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 批量审核请求类
 */
public class BatchReviewRequest {

    @NotBlank(message = "任务ID不能为空")
    private String taskId;

    @NotEmpty(message = "标注列表不能为空")
    @Valid
    private List<AnnotationReviewItem> annotations;

    /**
     * 标注审核项
     */
    public static class AnnotationReviewItem {
        
        @NotNull(message = "标注ID不能为空")
        private Long id;
        
        @NotBlank(message = "审核状态不能为空")
        @Size(max = 20, message = "审核状态不能超过20个字符")
        private String reviewStatus; // "approved" 或 "rejected"
        
        @Size(max = 1000, message = "审核意见不能超过1000个字符")
        private String reviewComment;

        // 构造函数
        public AnnotationReviewItem() {
        }

        public AnnotationReviewItem(Long id, String reviewStatus, String reviewComment) {
            this.id = id;
            this.reviewStatus = reviewStatus;
            this.reviewComment = reviewComment;
        }

        // Getters and Setters
        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getReviewStatus() {
            return reviewStatus;
        }

        public void setReviewStatus(String reviewStatus) {
            this.reviewStatus = reviewStatus;
        }

        public String getReviewComment() {
            return reviewComment;
        }

        public void setReviewComment(String reviewComment) {
            this.reviewComment = reviewComment;
        }
    }

    // 构造函数
    public BatchReviewRequest() {
    }

    public BatchReviewRequest(String taskId, List<AnnotationReviewItem> annotations) {
        this.taskId = taskId;
        this.annotations = annotations;
    }

    // Getters and Setters
    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public List<AnnotationReviewItem> getAnnotations() {
        return annotations;
    }

    public void setAnnotations(List<AnnotationReviewItem> annotations) {
        this.annotations = annotations;
    }
}
