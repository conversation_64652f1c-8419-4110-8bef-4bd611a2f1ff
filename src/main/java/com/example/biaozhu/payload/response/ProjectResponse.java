package com.example.biaozhu.payload.response;

import com.example.biaozhu.entity.Project;
import com.example.biaozhu.entity.User;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 项目响应类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProjectResponse {
    
    /**
     * 项目ID
     */
    private Long id;
    
    /**
     * 项目名称
     */
    private String name;
    
    /**
     * 项目描述
     */
    private String description;
    
    /**
     * 项目类型
     */
    private String type;
    
    /**
     * 项目状态
     */
    private String status;
    
    /**
     * 创建者
     */
    private UserSummary createdBy;
    
    /**
     * 截止日期
     */
    private Date deadline;
    
    /**
     * 创建时间
     */
    private Date createdAt;
    
    /**
     * 更新时间
     */
    private Date updatedAt;
    
    /**
     * 进度
     */
    private double progress;
    
    /**
     * 是否为活跃状态
     */
    private boolean active;
    
    /**
     * 数据集数量
     */
    private int datasetCount;
    
    /**
     * 任务数量
     */
    private int taskCount;
    
    /**
     * 团队成员
     */
    private List<UserSummary> members;
    
    /**
     * 项目标签
     */
    private String[] tags;
    
    /**
     * 从Project实体创建ProjectResponse
     * 
     * @param project 项目实体
     */
    public ProjectResponse(Project project) {
        this.id = project.getId();
        this.name = project.getName();
        this.description = project.getDescription();
        this.type = project.getType();
        this.status = project.getStatus();
        
        if (project.getCreatedBy() != null) {
            this.createdBy = new UserSummary(
                project.getCreatedBy().getId(),
                project.getCreatedBy().getUsername(),
                project.getCreatedBy().getFullName()
            );
        }
        
        this.deadline = project.getDeadline();
        this.createdAt = project.getCreatedAt();
        this.updatedAt = project.getUpdatedAt();
        this.progress = project.getProgress() != null ? project.getProgress() : 0.0;
        this.active = project.isActive();
        
        // 计算数据集数量和任务数量
        this.datasetCount = project.getDatasets() != null ? project.getDatasets().size() : 0;
        this.taskCount = project.getTasks() != null ? project.getTasks().size() : 0;
        
        this.members = new ArrayList<>();
        if (project.getMembers() != null) {
            for (User member : project.getMembers()) {
                this.members.add(new UserSummary(
                    member.getId(),
                    member.getUsername(),
                    member.getFullName()
                ));
            }
        }
        
        // 处理标签
        if (project.getTags() != null) {
            this.tags = project.getTags().split(",");
        }
    }
} 