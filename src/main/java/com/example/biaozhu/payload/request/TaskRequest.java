package com.example.biaozhu.payload.request;

import lombok.Data;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务请求类
 */
@Data
public class TaskRequest {
    
    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空")
    @Size(max = 100, message = "任务名称不能超过100个字符")
    private String name;
    
    /**
     * 任务描述
     */
    @Size(max = 500, message = "任务描述不能超过500个字符")
    private String description;
    
    /**
     * 任务类型
     */
    @NotBlank(message = "任务类型不能为空")
    private String taskType;
    
    /**
     * 数据集ID
     */
    @NotNull(message = "数据集ID不能为空")
    private Long datasetId;
    
    /**
     * 项目ID
     */
    private Long projectId;

    // 
    /**
     * 指定数据项ID列表
     */
    private List<Long> dataItemIds;
    
    /**
     * 标注模板ID
     */
    private Long templateId;
    
    /**
     * 优先级
     */
    private Integer priority;
    
    /**
     * 开始日期
     */
    private LocalDateTime startDate;
    
    /**
     * 截止日期
     */
    private LocalDateTime dueDate;
    
    /**
     * 分配给用户ID
     */
    private Long assignedToId;
    
    /**
     * 审核人ID
     */
    private Long reviewerId;
    
    /**
     * 任务指南
     */
    private String guidelines;
    
    /**
     * 标签ID列表
     */
    private List<Long> labelIds;

    public Integer getAnnotationCount() {
        return annotationCount;
    }

    /**
     * 标注数量（总的标注任务数量）
     */
    @Setter
    private Integer annotationCount;

    /**
     * 获取任务名称
     * @return 任务名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 获取任务描述
     * @return 任务描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 获取任务类型
     * @return 任务类型
     */
    public String getTaskType() {
        return taskType;
    }
    
    /**
     * 获取数据集ID
     * @return 数据集ID
     */
    public Long getDatasetId() {
        return datasetId;
    }
    
    /**
     * 获取项目ID
     * @return 项目ID
     */
    public Long getProjectId() {
        return projectId;
    }
    
    /**
     * 获取指定数据项ID列表
     * @return 数据项ID列表
     */
    public List<Long> getDataItemIds() {
        return dataItemIds;
    }
    
    /**
     * 获取标注模板ID
     * @return 标注模板ID
     */
    public Long getTemplateId() {
        return templateId;
    }
    
    /**
     * 获取优先级
     * @return 优先级
     */
    public Integer getPriority() {
        return priority;
    }
    
    /**
     * 获取开始日期
     * @return 开始日期
     */
    public LocalDateTime getStartDate() {
        return startDate;
    }
    
    /**
     * 获取截止日期
     * @return 截止日期
     */
    public LocalDateTime getDueDate() {
        return dueDate;
    }
    
    /**
     * 获取分配给用户ID
     * @return 用户ID
     */
    public Long getAssignedToId() {
        return assignedToId;
    }
    
    /**
     * 获取审核人ID
     * @return 审核人ID
     */
    public Long getReviewerId() {
        return reviewerId;
    }
    
    /**
     * 获取任务指南
     * @return 任务指南
     */
    public String getGuidelines() {
        return guidelines;
    }
    
    /**
     * 获取标签ID列表
     * @return 标签ID列表
     */
    public List<Long> getLabelIds() {
        return labelIds;
    }
} 